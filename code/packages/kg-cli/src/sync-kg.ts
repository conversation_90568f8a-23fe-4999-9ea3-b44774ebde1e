#!/usr/bin/env node

/**
 * @fileoverview sync-kg CLI command for incremental knowledge graph updates
 * @implements milestone-M1.2#CLIIntegration
 */

import { Command } from 'commander';
import { simpleGit } from 'simple-git';
import { writeFileSync, readFileSync, existsSync } from 'fs';
import { join, resolve } from 'path';
import { stringify as yamlStringify } from 'yaml';
import {
  diffGit,
  parseAnnotations,
  extractComponents,
  updateGraph,
  calculateCoverage,
  determineExitCode,
  type KnowledgeGraphNode,
  type KnowledgeGraphEdge,
  type MilestoneCoverage,
  type Annotation,
} from '@workflow-mapper/kg-sync-lib';
import { parseSpecsDirectory } from '@workflow-mapper/spec-parser-lib';

/**
 * Categorize and count errors by type for enhanced error handling
 * @param errors - Array of errors from processing
 * @returns Error categorization with counts
 */
function categorizeErrors(errors: Array<{ message: string; severity: string; file?: string; line?: number; }>) {
  let parseErrors = 0;
  let criticalErrors = 0;
  let warnings = 0;

  for (const error of errors) {
    if (error.severity === 'error') {
      // Parse errors: annotation format, parsing failures, validation errors
      if (
        error.message.includes('parse') ||
        error.message.includes('annotation') ||
        error.message.includes('format') ||
        error.message.includes('Invalid @implements') ||
        error.message.includes('not attached to') ||
        error.message.includes('milestone-') ||
        error.message.includes('component')
      ) {
        parseErrors++;
      } else {
        criticalErrors++;
      }
    } else if (error.severity === 'warning') {
      warnings++;
    }
  }

  return { parseErrors, criticalErrors, warnings };
}

/**
 * Enhanced exit code determination with parse error support (Task 11)
 * Exit codes: 0 (success), 60 (coverage breach), 70 (parse error), 1 (error)
 * @param coverageMetrics - Coverage metrics
 * @param errors - Processing errors
 * @param threshold - Coverage threshold
 * @returns Exit code according to milestone specification
 */
function determineEnhancedExitCode(
  coverageMetrics: MilestoneCoverage[],
  errors: Array<{ message: string; severity: string; file?: string; line?: number; }>,
  threshold: number = 0.5
): number {
  const { parseErrors, criticalErrors } = categorizeErrors(errors);

  // Critical errors take precedence (unexpected errors)
  if (criticalErrors > 0) {
    return 1; // Unexpected error
  }

  // Parse errors (annotation parsing failures)
  if (parseErrors > 0) {
    return 70; // Annotation parse error
  }

  // Coverage threshold check
  const belowThreshold = coverageMetrics.filter(m => m.coverage < threshold);
  if (belowThreshold.length > 0) {
    return 60; // Coverage breach
  }

  return 0; // Success
}

interface SyncOptions {
  since?: string;
  dryRun?: boolean;
  threshold?: number;
  outputDir?: string;
}

interface SyncResult {
  nodesAdded: number;
  nodesUpdated: number;
  nodesMarkedStale: number;
  edgesAdded: number;
  edgesUpdated: number;
  edgesMarkedStale: number;
  coverageMetrics: MilestoneCoverage[];
  errors: Array<{ message: string; severity: string; file?: string; line?: number; }>;
  parseErrors: number;
  criticalErrors: number;
  warnings: number;
  performance: {
    startTime: number;
    endTime: number;
    duration: number;
    filesProcessed: number;
    annotationsFound: number;
  };
}

interface KnowledgeGraph {
  '@context': Record<string, string>;
  '@graph': Array<KnowledgeGraphNode | KnowledgeGraphEdge>;
}

/**
 * Load existing knowledge graph from file
 * @param filePath - Path to knowledge graph file
 * @returns Knowledge graph or empty graph if file doesn't exist
 */
function loadKnowledgeGraph(filePath: string): {
  nodes: KnowledgeGraphNode[];
  edges: KnowledgeGraphEdge[];
} {
  if (!existsSync(filePath)) {
    return { nodes: [], edges: [] };
  }

  try {
    const content = readFileSync(filePath, 'utf-8');
    const graph: KnowledgeGraph = JSON.parse(content);

    const nodes: KnowledgeGraphNode[] = [];
    const edges: KnowledgeGraphEdge[] = [];

    for (const item of graph['@graph']) {
      if (item['@type'] === 'implements' || item['@type'] === 'dependsOn') {
        edges.push(item as KnowledgeGraphEdge);
      } else {
        nodes.push(item as KnowledgeGraphNode);
      }
    }

    return { nodes, edges };
  } catch (error) {
    console.warn(
      `Warning: Could not load existing knowledge graph from ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return { nodes: [], edges: [] };
  }
}

/**
 * Save knowledge graph to files
 * @param graph - Knowledge graph to save
 * @param outputDir - Output directory
 * @param dryRun - Whether this is a dry run
 */
function saveKnowledgeGraph(
  graph: { nodes: KnowledgeGraphNode[]; edges: KnowledgeGraphEdge[]; },
  outputDir: string,
  dryRun: boolean
): void {
  const knowledgeGraph: KnowledgeGraph = {
    '@context': {
      '@vocab': 'https://workflow-mapper.dev/vocab#',
      title: 'https://schema.org/name',
      description: 'https://schema.org/description',
      implements: 'https://workflow-mapper.dev/vocab#implements',
      dependsOn: 'https://workflow-mapper.dev/vocab#dependsOn',
    },
    '@graph': [...graph.nodes, ...graph.edges],
  };

  const jsonldContent = JSON.stringify(knowledgeGraph, null, 2);
  const yamlContent = yamlStringify(knowledgeGraph, { indent: 2 });

  if (!dryRun) {
    const jsonldPath = join(outputDir, 'kg.jsonld');
    const yamlPath = join(outputDir, 'kg.yaml');

    writeFileSync(jsonldPath, jsonldContent, 'utf-8');
    writeFileSync(yamlPath, yamlContent, 'utf-8');

    console.log(`✅ Knowledge graph saved to ${jsonldPath} and ${yamlPath}`);
  } else {
    console.log(
      '🔍 Dry run: Knowledge graph would be saved to kg.jsonld and kg.yaml'
    );
  }
}

/**
 * Save changes report
 * @param result - Sync result
 * @param outputDir - Output directory
 * @param dryRun - Whether this is a dry run
 */
function saveChangesReport(
  result: SyncResult,
  outputDir: string,
  dryRun: boolean
): void {
  const changesReport = {
    timestamp: new Date().toISOString(),
    summary: {
      nodesAdded: result.nodesAdded,
      nodesUpdated: result.nodesUpdated,
      nodesMarkedStale: result.nodesMarkedStale,
      edgesAdded: result.edgesAdded,
      edgesUpdated: result.edgesUpdated,
      edgesMarkedStale: result.edgesMarkedStale,
    },
    coverage: result.coverageMetrics,
    errors: result.errors,
  };

  const reportContent = JSON.stringify(changesReport, null, 2);

  if (!dryRun) {
    const reportPath = join(outputDir, 'kg-changes.json');
    writeFileSync(reportPath, reportContent, 'utf-8');
    console.log(`📊 Changes report saved to ${reportPath}`);
  } else {
    console.log('🔍 Dry run: Changes report would be saved to kg-changes.json');
  }
}

/**
 * Print coverage table
 * @param coverageMetrics - Coverage metrics to display
 */
function printCoverageTable(coverageMetrics: MilestoneCoverage[]): void {
  if (coverageMetrics.length === 0) {
    console.log('📊 No milestone coverage data available');
    return;
  }

  console.log('\n📊 Milestone Coverage Report:');
  console.log(
    '┌─────────────┬───────────┬──────────────┬──────────┬────────────┐'
  );
  console.log(
    '│ Milestone   │ Total     │ Implemented  │ Coverage │ Confidence │'
  );
  console.log(
    '├─────────────┼───────────┼──────────────┼──────────┼────────────┤'
  );

  for (const metric of coverageMetrics) {
    const coverage = (metric.coverage * 100).toFixed(1);
    const confidence = (metric.confidence * 100).toFixed(1);

    console.log(
      `│ ${metric.milestoneId.padEnd(11)} │ ${metric.totalComponents.toString().padStart(9)} │ ${metric.implementedComponents.toString().padStart(12)} │ ${coverage.padStart(7)}% │ ${confidence.padStart(9)}% │`
    );
  }

  console.log(
    '└─────────────┴───────────┴──────────────┴──────────┴────────────┘'
  );
}

/**
 * Main sync-kg command implementation
 * @param specsDirectory - Directory containing specifications
 * @param options - Command options
 */
async function syncKnowledgeGraph(
  specsDirectory: string,
  options: SyncOptions
): Promise<number> {
  const performanceStart = Date.now();
  const outputDir = options.outputDir || process.cwd();

  // Initialize performance tracking and error collection
  let filesProcessed = 0;
  let annotationsFound = 0;
  const allErrors: Array<{ message: string; severity: string; file?: string; line?: number; }> = [];

  try {
    console.log('🚀 Starting incremental knowledge graph sync...');
    console.log(`📊 Performance tracking enabled`);

    // Initialize git
    const git = simpleGit();

    // Get repository root
    const repoRoot = await git.revparse(['--show-toplevel']);
    const repoRootPath = repoRoot.trim();

    // Determine since reference
    let sinceRef = options.since;
    if (!sinceRef) {
      try {
        sinceRef = await git.raw(['merge-base', 'HEAD', 'origin/main']);
        sinceRef = sinceRef.trim();
        console.log(`📍 Using merge-base with origin/main: ${sinceRef}`);
      } catch (error) {
        sinceRef = 'HEAD~1';
        console.log(
          `⚠️  Could not find merge-base, using HEAD~1: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    }

    // Get changed files
    console.log(`🔍 Detecting changes since ${sinceRef}...`);
    const diffResult = await diffGit({ since: sinceRef });
    const changedFiles = [...diffResult.changedFiles, ...diffResult.addedFiles];
    console.log(`📁 Found ${changedFiles.length} changed files`);

    if (changedFiles.length === 0) {
      console.log('✅ No changes detected, knowledge graph is up to date');
      return 0;
    }

    // Parse annotations from changed files
    console.log('🔍 Parsing annotations from changed files...');
    const annotations: Annotation[] = [];

    for (const filePath of changedFiles) {
      try {
        // Only process TypeScript/JavaScript files
        if (filePath.match(/\.(ts|tsx|js|jsx)$/)) {
          filesProcessed++;
          // Resolve file path relative to repository root
          const resolvedPath = join(repoRootPath, filePath);
          const fileContent = readFileSync(resolvedPath, 'utf-8');
          const fileAnnotations = parseAnnotations(fileContent, filePath);

          // Collect errors from annotations with file context
          for (const annotation of fileAnnotations) {
            for (const error of annotation.errors) {
              allErrors.push({
                message: error.message,
                severity: error.severity,
                file: filePath,
                line: error.line,
              });
            }
          }

          annotations.push(...fileAnnotations);
          annotationsFound += fileAnnotations.length;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.warn(`⚠️  Could not parse annotations from ${filePath}: ${errorMessage}`);

        // Add critical error for file parsing failure
        allErrors.push({
          message: `Failed to parse file: ${errorMessage}`,
          severity: 'error',
          file: filePath,
        });
      }
    }

    console.log(`📝 Found ${annotations.length} annotations`);

    // Load existing knowledge graph
    const kgPath = join(outputDir, 'kg.jsonld');
    const currentGraph = loadKnowledgeGraph(kgPath);

    // Update knowledge graph
    console.log('🔄 Updating knowledge graph...');
    const updateResult = updateGraph(currentGraph, annotations, changedFiles);

    // Parse specifications to get component information
    console.log('📋 Extracting components from specifications...');
    const parseResult = parseSpecsDirectory(specsDirectory);
    const allComponents = new Map<string, number>();

    for (const spec of parseResult.specs) {
      // Extract milestone ID from spec
      const milestoneMatch = spec.id.match(/milestone-([^-]+)/);
      if (milestoneMatch) {
        const milestoneId = `M${milestoneMatch[1]}`;
        const components = extractComponents(spec.content, milestoneId);
        allComponents.set(milestoneId, components.length);
      }
    }

    // Calculate coverage metrics
    console.log('📊 Calculating coverage metrics...');
    const coverageMetrics: MilestoneCoverage[] = [];

    for (const [milestoneId, totalComponents] of allComponents) {
      const coverage = calculateCoverage(
        milestoneId,
        currentGraph.edges,
        totalComponents
      );
      coverageMetrics.push(coverage);
    }

    // Combine all errors (annotation errors + update errors)
    const combinedErrors = [
      ...allErrors,
      ...updateResult.errors.map((e) => ({
        message: e.message,
        severity: e.severity,
        file: undefined,
        line: undefined,
      })),
    ];

    // Calculate performance metrics
    const performanceEnd = Date.now();
    const duration = performanceEnd - performanceStart;

    // Categorize errors for enhanced reporting
    const { parseErrors, criticalErrors, warnings } = categorizeErrors(combinedErrors);

    // Prepare result with enhanced error handling and performance metrics
    const result: SyncResult = {
      nodesAdded: updateResult.nodesAdded,
      nodesUpdated: updateResult.nodesUpdated,
      nodesMarkedStale: updateResult.nodesMarkedStale,
      edgesAdded: updateResult.edgesAdded,
      edgesUpdated: updateResult.edgesUpdated,
      edgesMarkedStale: updateResult.edgesMarkedStale,
      coverageMetrics,
      errors: combinedErrors,
      parseErrors,
      criticalErrors,
      warnings,
      performance: {
        startTime: performanceStart,
        endTime: performanceEnd,
        duration,
        filesProcessed,
        annotationsFound,
      },
    };

    // Save outputs
    saveKnowledgeGraph(currentGraph, outputDir, options.dryRun || false);
    saveChangesReport(result, outputDir, options.dryRun || false);

    // Print coverage table
    printCoverageTable(coverageMetrics);

    // Print enhanced summary with performance metrics
    console.log(`\n✅ Sync completed in ${result.performance.duration}ms`);
    console.log(
      `📊 Summary: +${result.nodesAdded} nodes, ~${result.nodesUpdated} updated, +${result.edgesAdded} edges, ~${result.edgesUpdated} updated`
    );
    console.log(
      `📈 Performance: ${result.performance.filesProcessed} files processed, ${result.performance.annotationsFound} annotations found`
    );

    // Enhanced error reporting with categorization
    if (result.errors.length > 0) {
      console.log(`\n⚠️  Error Summary: ${result.errors.length} total (${result.parseErrors} parse, ${result.criticalErrors} critical, ${result.warnings} warnings)`);

      // Show detailed errors with file context
      for (const error of result.errors) {
        const location = error.file ? `${error.file}${error.line ? `:${error.line}` : ''}` : 'unknown';
        console.log(`   ${error.severity.toUpperCase()}: ${error.message} (${location})`);
      }
    }

    // Use enhanced exit code determination (Task 11)
    const exitCode = determineEnhancedExitCode(coverageMetrics, result.errors, options.threshold);

    if (exitCode === 60) {
      console.log('❌ Coverage threshold breach detected');
      const failedMilestones = coverageMetrics.filter(
        (m) => m.coverage < (options.threshold || 0.5)
      );
      for (const milestone of failedMilestones) {
        console.log(
          `   ${milestone.milestoneId}: ${(milestone.coverage * 100).toFixed(1)}% < ${((options.threshold || 0.5) * 100).toFixed(1)}%`
        );
      }
    }

    return exitCode;
  } catch (error) {
    console.error(
      '❌ Sync failed:',
      error instanceof Error ? error.message : 'Unknown error'
    );
    return 1; // General error
  }
}

// CLI setup
const program = new Command();

program
  .name('sync-kg')
  .description('Incremental knowledge graph synchronization')
  .version('1.2.0')
  .argument('<specs-directory>', 'Directory containing specification files')
  .option(
    '--since <commit-ish>',
    'Git reference to compare against (default: merge-base with origin/main)'
  )
  .option('--dry-run', 'Show what would be done without making changes')
  .option(
    '--threshold <number>',
    'Coverage threshold for exit code (default: 0.5)',
    parseFloat
  )
  .option(
    '--output-dir <path>',
    'Output directory for generated files (default: current directory)'
  )
  .action(async (specsDirectory: string, options: SyncOptions) => {
    const resolvedSpecsDir = resolve(specsDirectory);
    const exitCode = await syncKnowledgeGraph(resolvedSpecsDir, options);
    process.exit(exitCode);
  });

// Handle unhandled errors
process.on('unhandledRejection', (reason) => {
  console.error('❌ Unhandled rejection:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception:', error);
  process.exit(1);
});

// Parse command line arguments
program.parse();
