/**
 * @fileoverview Tests for git diff functionality
 */

import { diffGit } from '../src/diffGit.js';
import type { SyncOptions } from '../src/types.js';

// Mock simple-git
jest.mock('simple-git', () => ({
  simpleGit: jest.fn(() => ({
    diffSummary: jest.fn(),
    status: jest.fn(),
  })),
}));

import { simpleGit } from 'simple-git';

const mockGit = {
  diffSummary: jest.fn(),
  status: jest.fn(),
  checkIsRepo: jest.fn(),
  revparse: jest.fn(),
};

(simpleGit as jest.Mock).mockReturnValue(mockGit);

describe('diffGit', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default mocks for successful operations
    mockGit.checkIsRepo.mockResolvedValue(true);
    mockGit.revparse.mockResolvedValue('abc123');
    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [],
      not_added: [],
    });
  });

  it('should detect changed files', async () => {
    // Mock git diff response
    mockGit.diffSummary.mockResolvedValue({
      files: [
        {
          file: 'src/test.ts',
          insertions: 5,
          deletions: 2,
          binary: false,
        },
        {
          file: 'src/new.ts',
          insertions: 10,
          deletions: 0,
          binary: false,
        },
        {
          file: 'src/deleted.ts',
          insertions: 0,
          deletions: 8,
          binary: false,
        },
      ],
    });

    mockGit.status.mockResolvedValue({
      renamed: [],
    });

    const options: SyncOptions = {
      since: 'HEAD~1',
      verbose: false,
    };

    const result = await diffGit(options);

    expect(result.changedFiles).toEqual(['src/test.ts']);
    expect(result.addedFiles).toEqual(['src/new.ts']);
    expect(result.deletedFiles).toEqual(['src/deleted.ts']);
    expect(result.renamedFiles).toEqual([]);
    expect(result.errors).toEqual([]);
  });

  it('should detect renamed files', async () => {
    mockGit.diffSummary.mockResolvedValue({
      files: [],
    });

    mockGit.status.mockResolvedValue({
      renamed: [
        {
          from: 'src/old.ts',
          to: 'src/new.ts',
        },
      ],
    });

    const options: SyncOptions = {
      since: 'origin/main',
    };

    const result = await diffGit(options);

    expect(result.renamedFiles).toEqual([
      {
        from: 'src/old.ts',
        to: 'src/new.ts',
      },
    ]);
  });

  it('should filter out binary and non-source files', async () => {
    mockGit.diffSummary.mockResolvedValue({
      files: [
        {
          file: 'src/test.ts',
          insertions: 5,
          deletions: 0,
          binary: false,
        },
        {
          file: 'image.png',
          insertions: 0,
          deletions: 0,
          binary: true,
        },
        {
          file: 'config.json',
          insertions: 2,
          deletions: 0,
          binary: false,
        },
      ],
    });

    mockGit.status.mockResolvedValue({
      renamed: [],
    });

    const options: SyncOptions = {};
    const result = await diffGit(options);

    expect(result.addedFiles).toEqual(['src/test.ts']);
    expect(result.addedFiles).not.toContain('image.png');
    expect(result.addedFiles).not.toContain('config.json');
  });

  it('should handle git errors gracefully', async () => {
    mockGit.diffSummary.mockRejectedValue(new Error('Git command failed'));

    const options: SyncOptions = {
      since: 'invalid-ref',
    };

    const result = await diffGit(options);

    expect(result.errors).toEqual(['Git diff failed: Git command failed']);
    expect(result.changedFiles).toEqual([]);
    expect(result.addedFiles).toEqual([]);
    expect(result.deletedFiles).toEqual([]);
  });

  it('should use default since value when not provided', async () => {
    mockGit.diffSummary.mockResolvedValue({
      files: [],
    });

    mockGit.status.mockResolvedValue({
      renamed: [],
    });

    const options: SyncOptions = {};
    await diffGit(options);

    expect(mockGit.diffSummary).toHaveBeenCalledWith(['HEAD~1']);
  });
});
