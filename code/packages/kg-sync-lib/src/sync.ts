/**
 * @fileoverview Main sync orchestration function
 * @implements milestone-M1.2#SyncOrchestrator
 */

import { diffGit } from './diffGit.js';
import { parseAnnotations } from './parseAnnotations.js';
import { updateGraph } from './updateGraph.js';
import { calculateCoverage } from './confidence.js';
<<<<<<< HEAD
import type {
  SyncOptions,
  SyncResult,
  MilestoneCoverage,
  GraphError,
} from './types.js';
=======
import type { SyncOptions, SyncResult, MilestoneCoverage, GraphError } from './types.js';
>>>>>>> milestone-M1.2/task-12-unit-tests
import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * Main sync function that orchestrates the entire bidirectional sync process
 * @param directory - Directory containing specifications
 * @param options - Sync options
 * @returns Promise resolving to sync result
 */
export async function syncKnowledgeGraph(
  directory: string,
  options: SyncOptions = {}
): Promise<SyncResult> {
  const errors: GraphError[] = [];
  const warnings: GraphError[] = [];

  try {
    if (options.verbose) {
      console.log(`Starting knowledge graph sync for directory: ${directory}`);
    }

    // Step 1: Get git diff to find changed files
    const diffResult = await diffGit(options);

    if (options.verbose) {
<<<<<<< HEAD
      console.log(
        `Found ${diffResult.changedFiles.length + diffResult.addedFiles.length} changed files`
      );
=======
      console.log(`Found ${diffResult.changedFiles.length + diffResult.addedFiles.length} changed files`);
>>>>>>> milestone-M1.2/task-12-unit-tests
    }

    // Step 2: Parse annotations from changed files
    const allAnnotations = [];
    const changedFiles = [...diffResult.changedFiles, ...diffResult.addedFiles];

    for (const filePath of changedFiles) {
      try {
<<<<<<< HEAD
        const fileContent = readFileSync(
          join(process.cwd(), filePath),
          'utf-8'
        );
=======
        const fileContent = readFileSync(join(process.cwd(), filePath), 'utf-8');
>>>>>>> milestone-M1.2/task-12-unit-tests
        const annotations = parseAnnotations(fileContent, filePath);
        allAnnotations.push(...annotations);
      } catch (error) {
        warnings.push({
          type: 'parse',
          message: `Failed to read file ${filePath}: ${error instanceof Error ? error.message : String(error)}`,
          filePath,
<<<<<<< HEAD
          severity: 'warning',
=======
          severity: 'warning'
>>>>>>> milestone-M1.2/task-12-unit-tests
        });
      }
    }

    if (options.verbose) {
      console.log(`Parsed ${allAnnotations.length} annotations`);
    }

    // Step 3: Update knowledge graph
    const graphUpdateResult = updateGraph(
      { nodes: [], edges: [] }, // Empty current graph for now
      allAnnotations,
      changedFiles
    );

    // Step 4: Calculate coverage metrics
    const coverageMetrics: MilestoneCoverage[] = [];
<<<<<<< HEAD
    const milestoneIds = new Set(
      allAnnotations.map((a) => a.milestoneId).filter(Boolean)
    );
=======
    const milestoneIds = new Set(allAnnotations.map(a => a.milestoneId).filter(Boolean));
>>>>>>> milestone-M1.2/task-12-unit-tests

    for (const milestoneId of milestoneIds) {
      try {
        // For now, assume 10 total components per milestone (this would come from spec parsing)
        const coverage = calculateCoverage(milestoneId, [], 10);
        coverageMetrics.push(coverage);
      } catch (error) {
        warnings.push({
          type: 'validation',
          message: `Failed to calculate coverage for ${milestoneId}: ${error instanceof Error ? error.message : String(error)}`,
<<<<<<< HEAD
          severity: 'warning',
=======
          severity: 'warning'
>>>>>>> milestone-M1.2/task-12-unit-tests
        });
      }
    }

    // Step 5: Determine exit code based on coverage
    let exitCode = 0;
<<<<<<< HEAD
    const lowCoverageMilestones = coverageMetrics.filter(
      (m) => m.coverage < 0.5
    );
=======
    const lowCoverageMilestones = coverageMetrics.filter(m => m.coverage < 0.5);
>>>>>>> milestone-M1.2/task-12-unit-tests

    if (lowCoverageMilestones.length > 0) {
      exitCode = 60; // Coverage breach
      errors.push({
        type: 'validation',
<<<<<<< HEAD
        message: `Coverage below threshold for milestones: ${lowCoverageMilestones.map((m) => m.milestoneId).join(', ')}`,
        severity: 'error',
=======
        message: `Coverage below threshold for milestones: ${lowCoverageMilestones.map(m => m.milestoneId).join(', ')}`,
        severity: 'error'
>>>>>>> milestone-M1.2/task-12-unit-tests
      });
    }

    // Check for annotation parse errors
<<<<<<< HEAD
    const annotationErrors = allAnnotations.flatMap((a) =>
      a.errors.filter((e) => e.severity === 'error')
    );
=======
    const annotationErrors = allAnnotations.flatMap(a => a.errors.filter(e => e.severity === 'error'));
>>>>>>> milestone-M1.2/task-12-unit-tests
    if (annotationErrors.length > 0) {
      exitCode = 70; // Parse error
    }

    const success = exitCode === 0;

    if (options.verbose) {
      console.log(`Sync completed with exit code: ${exitCode}`);
    }

    return {
      success,
      graphUpdateResult,
      diffResult,
      coverageMetrics,
      exitCode,
      errors,
      warnings,
    };
<<<<<<< HEAD
=======

>>>>>>> milestone-M1.2/task-12-unit-tests
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    errors.push({
      type: 'update',
      message: `Sync failed: ${errorMessage}`,
<<<<<<< HEAD
      severity: 'error',
=======
      severity: 'error'
>>>>>>> milestone-M1.2/task-12-unit-tests
    });

    return {
      success: false,
      graphUpdateResult: {
        nodesAdded: 0,
        nodesUpdated: 0,
        nodesMarkedStale: 0,
        edgesAdded: 0,
        edgesUpdated: 0,
        edgesMarkedStale: 0,
        coverageMetrics: [],
<<<<<<< HEAD
        errors: [
          {
            type: 'update',
            message: errorMessage,
            severity: 'error',
          },
        ],
=======
        errors: [{
          type: 'update',
          message: errorMessage,
          severity: 'error'
        }],
>>>>>>> milestone-M1.2/task-12-unit-tests
      },
      diffResult: {
        changedFiles: [],
        addedFiles: [],
        deletedFiles: [],
        renamedFiles: [],
        errors: [errorMessage],
      },
      coverageMetrics: [],
      exitCode: 1, // Unexpected error
      errors,
      warnings,
    };
  }
}
