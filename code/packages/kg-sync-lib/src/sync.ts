/**
 * @fileoverview Main sync orchestration function
 * @implements milestone-M1.2#SyncOrchestrator
 */

import { diffGit } from './diffGit.js';
import { parseAnnotations } from './parseAnnotations.js';
import { updateGraph } from './updateGraph.js';
import { calculateCoverage } from './confidence.js';
import type { SyncOptions, SyncResult, MilestoneCoverage, GraphError } from './types.js';
import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * Main sync function that orchestrates the entire bidirectional sync process
 * @param directory - Directory containing specifications
 * @param options - Sync options
 * @returns Promise resolving to sync result
 */
export async function syncKnowledgeGraph(
  directory: string,
  options: SyncOptions = {}
): Promise<SyncResult> {
  const errors: GraphError[] = [];
  const warnings: GraphError[] = [];

  try {
    if (options.verbose) {
      console.log(`Starting knowledge graph sync for directory: ${directory}`);
    }

    // Step 1: Get git diff to find changed files
    const diffResult = await diffGit(options);

    if (options.verbose) {
      console.log(`Found ${diffResult.changedFiles.length + diffResult.addedFiles.length} changed files`);
    }

    // Step 2: Parse annotations from changed files
    const allAnnotations = [];
    const changedFiles = [...diffResult.changedFiles, ...diffResult.addedFiles];

    for (const filePath of changedFiles) {
      try {
        const fileContent = readFileSync(join(process.cwd(), filePath), 'utf-8');
        const annotations = parseAnnotations(fileContent, filePath);
        allAnnotations.push(...annotations);
      } catch (error) {
        warnings.push({
          type: 'parse',
          message: `Failed to read file ${filePath}: ${error instanceof Error ? error.message : String(error)}`,
          filePath,
          severity: 'warning'
        });
      }
    }

    if (options.verbose) {
      console.log(`Parsed ${allAnnotations.length} annotations`);
    }

    // Step 3: Update knowledge graph
    const graphUpdateResult = updateGraph(
      { nodes: [], edges: [] }, // Empty current graph for now
      allAnnotations,
      changedFiles
    );

    // Step 4: Calculate coverage metrics
    const coverageMetrics: MilestoneCoverage[] = [];
    const milestoneIds = new Set(allAnnotations.map(a => a.milestoneId).filter(Boolean));

    for (const milestoneId of milestoneIds) {
      try {
        // For now, assume 10 total components per milestone (this would come from spec parsing)
        const coverage = calculateCoverage(milestoneId, [], 10);
        coverageMetrics.push(coverage);
      } catch (error) {
        warnings.push({
          type: 'validation',
          message: `Failed to calculate coverage for ${milestoneId}: ${error instanceof Error ? error.message : String(error)}`,
          severity: 'warning'
        });
      }
    }

    // Step 5: Determine exit code based on coverage
    let exitCode = 0;
    const lowCoverageMilestones = coverageMetrics.filter(m => m.coverage < 0.5);

    if (lowCoverageMilestones.length > 0) {
      exitCode = 60; // Coverage breach
      errors.push({
        type: 'validation',
        message: `Coverage below threshold for milestones: ${lowCoverageMilestones.map(m => m.milestoneId).join(', ')}`,
        severity: 'error'
      });
    }

    // Check for annotation parse errors
    const annotationErrors = allAnnotations.flatMap(a => a.errors.filter(e => e.severity === 'error'));
    if (annotationErrors.length > 0) {
      exitCode = 70; // Parse error
    }

    const success = exitCode === 0;

    if (options.verbose) {
      console.log(`Sync completed with exit code: ${exitCode}`);
    }

    return {
      success,
      graphUpdateResult,
      diffResult,
      coverageMetrics,
      exitCode,
      errors,
      warnings,
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    errors.push({
      type: 'update',
      message: `Sync failed: ${errorMessage}`,
      severity: 'error'
    });

    return {
      success: false,
      graphUpdateResult: {
        nodesAdded: 0,
        nodesUpdated: 0,
        nodesMarkedStale: 0,
        edgesAdded: 0,
        edgesUpdated: 0,
        edgesMarkedStale: 0,
        coverageMetrics: [],
        errors: [{
          type: 'update',
          message: errorMessage,
          severity: 'error'
        }],
      },
      diffResult: {
        changedFiles: [],
        addedFiles: [],
        deletedFiles: [],
        renamedFiles: [],
        errors: [errorMessage],
      },
      coverageMetrics: [],
      exitCode: 1, // Unexpected error
      errors,
      warnings,
    };
  }
}
