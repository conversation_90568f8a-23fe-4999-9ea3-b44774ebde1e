/**
 * @fileoverview Git diff detection using simple-git
 * @implements milestone-M1.2#GitDiffCore
 */

import { simpleGit } from 'simple-git';
import type { GitDiffResult, SyncOptions } from './types.js';

/**
 * Detect changed files using git diff
 * @param options - Sync options including since reference
 * @returns Promise resolving to git diff result
 */
export async function diffGit(options: SyncOptions): Promise<GitDiffResult> {
  const git = simpleGit();
  const result: GitDiffResult = {
    changedFiles: [],
    addedFiles: [],
    deletedFiles: [],
    renamedFiles: [],
    errors: [],
  };

  try {
    // Determine the comparison reference
    const since = options.since || 'HEAD~1';

    // Get the diff summary
    const diffSummary = await git.diffSummary([since]);

    // Process each file in the diff
    for (const file of diffSummary.files) {
      const filePath = file.file;

      // Skip binary files and non-source files
      if (file.binary || !isSourceFile(filePath)) {
        continue;
      }

      // Categorize the file change
      if (file.insertions > 0 && file.deletions === 0) {
        result.addedFiles.push(filePath);
      } else if (file.insertions === 0 && file.deletions > 0) {
        result.deletedFiles.push(filePath);
      } else {
        result.changedFiles.push(filePath);
      }
    }

    // Check for renamed files
    const status = await git.status();
    for (const file of status.renamed) {
      result.renamedFiles.push({
        from: file.from,
        to: file.to,
      });
    }

    if (options.verbose) {
      console.log(`Git diff detected:`);
      console.log(`  Changed: ${result.changedFiles.length} files`);
      console.log(`  Added: ${result.addedFiles.length} files`);
      console.log(`  Deleted: ${result.deletedFiles.length} files`);
      console.log(`  Renamed: ${result.renamedFiles.length} files`);
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    result.errors.push(`Git diff failed: ${errorMessage}`);

    if (options.verbose) {
      console.error(`Git diff error: ${errorMessage}`);
    }
  }

  return result;
}

/**
 * Check if a file is a source file that should be processed
 * @param filePath - Path to the file
 * @returns True if the file should be processed
 */
function isSourceFile(filePath: string): boolean {
  const sourceExtensions = ['.ts', '.tsx', '.js', '.jsx', '.py', '.mdx', '.md'];
  return sourceExtensions.some((ext) => filePath.endsWith(ext));
}
