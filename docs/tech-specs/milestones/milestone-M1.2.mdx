---
title: Milestone M1.1 — Bidirectional Sync & Incremental Diff
description: Link code ↔ specs via annotations, update the KG on every git diff, and emit confidence / coverage metrics.
created: 2025-05-29
updated: 2025-06-01
version: 0.3.3
status: Draft
tags: [milestone]
authors: [WorkflowMapper Team]
---

import { Callout } from '@/components/Callout'

<Callout emoji="↔️">
<strong>Objective:</strong> Close the loop—when specs change, generate TODO stubs in code; when code changes, refresh <code>kg.*</code> and flag stale specs.<br/>
All updates run incrementally via <code>git diff</code>; the KG gains <code>confidence</code> and <code>last_verified</code> metadata.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
node: "20.11.0"
pnpm: "8.15.4"
typescript: "5.4.3"
simple-git: "3.22.0"          # git diff wrapper
comment-parser: "1.4.0"       # parse JS/TS docblocks
regexparam: "2.0.0"           # lightweight pattern match
jest: "29.7.0"
# plus dependencies from M0.2 and M1—keep same pin versions
```

---

## 🎯 Definition of Done

1. **Annotations**: JS/TS functions annotated with
   ```js
   /** @implements milestone-MX #ComponentName */
   ```
   are parsed into implements edges (confidence = 1.0).
2. **Incremental CLI**: `pnpm run sync-kg -- --since <gitRef>`
   - Scans only changed files & specs.
   - Updates kg.jsonld & kg.yaml.
   - Produces a diff report (`kg-changes.json`) listing nodes/edges added, removed, or flagged stale.
3. **Confidence & coverage**: Each milestone node gains
   - `implementation_coverage` (0-1)
   - `confidence` (0-1 avg of edges).
4. **CI job** `sync-diff` runs on PR; fails if coverage of any milestone falls below 0.5.
5. **Unit-test coverage** for new sync code ≥ 80 %.

---

## 📦 Deliverables

| Artefact / Path                        | Content                                              |
|----------------------------------------|------------------------------------------------------|
| code/packages/kg-sync-lib/             | diffGit.ts, updateGraph.ts, tests                    |
| code/packages/kg-cli/                  | new command sync-kg.ts + bin entry                   |
| kg-changes.json                        | JSON diff report (only in working tree; not committed) |
| .github/workflows/sync-diff.yml        | CI flow for incremental sync                         |
| docs/tech-specs/domains/kg-sync.mdx    | Domain spec: annotation grammar, diff algorithm      |

---

## 🗂 Directory Additions

```text
code/packages/
└─ kg-sync-lib/
   ├─ src/
   │  ├─ diffGit.ts
   │  ├─ updateGraph.ts
   │  └─ index.ts
   ├─ tests/
   └─ package.json
```

---

## 🧠 Key Decisions

| Topic             | Decision                                         | Rationale                                 |
|-------------------|-------------------------------------------------|-------------------------------------------|
| Annotation format | @implements milestone-ID#Component in JSDoc/TSDoc| Simple, language-agnostic.                |
| Diff source       | Use simple-git to get changed paths vs origin/main| No external Git install assumptions in CI. |
| Stale detection   | If annotated component no longer exists, mark edge confidence = 0.2 and add stale: true. | Highlights technical debt without deleting history. |
| Coverage metric   | implementedComponents / totalComponents per milestone. | Enables red/yellow/green dashboards later. |

---

## � Technical Specifications

### 📝 Annotation Parsing Algorithm

**Input**: Source code file content (string)
**Output**: Array of validated annotation objects

```typescript
interface Annotation {
  milestoneId: string;        // e.g., "M1.2"
  componentName: string;      // e.g., "AnnotationParser"
  functionName: string;       // e.g., "parseAnnotations"
  filePath: string;          // e.g., "src/parser.ts"
  lineNumber: number;        // Line where annotation appears
  confidence: number;        // Always 1.0 for valid annotations
  lastVerified: string;      // ISO timestamp
  errors: ParseError[];      // Validation errors if any
}

interface ParseError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning';
  suggestion?: string;
}
```

**Parsing Steps**:
1. **Extract Comments**: Use comment-parser to get all JSDoc blocks
2. **Filter @implements**: Find comments containing @implements tags
3. **Validate Format**: Check pattern `milestone-M\d+(\.\d+)*#[A-Za-z_][A-Za-z0-9_]*`
4. **Extract Components**: Parse milestone ID and component name
5. **Validate Context**: Ensure annotation is on function/class/method
6. **Generate Metadata**: Add file path, line numbers, timestamps

**Error Handling**:
- **Invalid Format**: Log warning, skip annotation, continue parsing
- **Missing Function**: Error - annotation not attached to code element
- **Duplicate Annotations**: Warning - multiple @implements for same component
- **Invalid Milestone ID**: Error - milestone ID doesn't match pattern

### 🔄 Graph Update Logic

**Input**: Current knowledge graph + new annotations + changed files
**Output**: Updated knowledge graph with confidence scores

```typescript
interface GraphUpdateResult {
  nodesAdded: number;
  nodesUpdated: number;
  nodesMarkedStale: number;
  edgesAdded: number;
  edgesUpdated: number;
  edgesMarkedStale: number;
  coverageMetrics: MilestoneCoverage[];
  errors: GraphError[];
}

interface MilestoneCoverage {
  milestoneId: string;
  totalComponents: number;
  implementedComponents: number;
  coverage: number;           // 0.0 to 1.0
  confidence: number;         // Average confidence of all edges
  lastUpdated: string;        // ISO timestamp
}
```

**Update Algorithm**:

1. **Load Existing Graph**: Parse current kg.jsonld
2. **Process Changed Files**: For each file in git diff:
   - Parse annotations from file content
   - Compare with existing annotations for this file
   - Identify added/removed/modified annotations
3. **Update Nodes**:
   - Add new function nodes for annotated functions
   - Update existing nodes with new metadata
   - Mark nodes as stale if function no longer exists
4. **Update Edges**:
   - Create "implements" edges: function → milestone component
   - Set confidence = 1.0 for valid annotations
   - Mark edges as stale if annotation removed
5. **Calculate Coverage**: For each milestone:
   - Count total components (from milestone specs)
   - Count implemented components (from implements edges)
   - Calculate coverage = implemented / total
6. **Merge Results**: Combine with unchanged parts of graph

**Stale Detection Logic**:
```typescript
// If function exists but annotation removed
edge.confidence = 0.2;
edge.stale = true;
edge.staleReason = "annotation_removed";
edge.lastVerified = currentTimestamp;

// If function no longer exists
edge.confidence = 0.1;
edge.stale = true;
edge.staleReason = "function_deleted";
edge.lastVerified = currentTimestamp;
```

### 📊 Confidence Scoring System

**Confidence Levels**:
- **1.0**: Valid annotation, function exists, recently verified
- **0.8**: Valid annotation, function exists, not recently verified (>30 days)
- **0.5**: Annotation exists but has validation warnings
- **0.2**: Annotation removed but function still exists (stale)
- **0.1**: Function deleted but edge preserved for history (stale)

**Scoring Algorithm**:
```typescript
function calculateConfidence(annotation: Annotation, functionExists: boolean): number {
  if (!functionExists) return 0.1;
  if (annotation.errors.some(e => e.severity === 'error')) return 0.5;

  const daysSinceVerified = daysBetween(annotation.lastVerified, now());
  if (daysSinceVerified > 30) return 0.8;

  return 1.0;
}

function calculateMilestoneConfidence(milestone: Milestone): number {
  const implementsEdges = getImplementsEdges(milestone.id);
  if (implementsEdges.length === 0) return 0.0;

  const totalConfidence = implementsEdges.reduce((sum, edge) => sum + edge.confidence, 0);
  return totalConfidence / implementsEdges.length;
}
```

**Coverage Calculation**:
```typescript
function calculateCoverage(milestoneId: string): MilestoneCoverage {
  const milestone = getMilestone(milestoneId);
  const components = extractComponents(milestone.content); // Parse milestone spec
  const implementsEdges = getImplementsEdges(milestoneId);

  const implementedComponents = new Set(
    implementsEdges
      .filter(edge => edge.confidence > 0.2) // Exclude very stale
      .map(edge => edge.componentName)
  );

  return {
    milestoneId,
    totalComponents: components.length,
    implementedComponents: implementedComponents.size,
    coverage: implementedComponents.size / components.length,
    confidence: calculateMilestoneConfidence(milestone),
    lastUpdated: new Date().toISOString()
  };
}
```

**Exit Code Logic**:
```typescript
function determineExitCode(coverageMetrics: MilestoneCoverage[]): number {
  const belowThreshold = coverageMetrics.filter(m => m.coverage < 0.5);

  if (belowThreshold.length > 0) {
    console.log(`Coverage breach: ${belowThreshold.length} milestones below 50%`);
    return 60; // Coverage breach
  }

  return 0; // Success
}
```

### 🧩 Component Extraction Logic

**Purpose**: Extract component names from milestone specifications to calculate coverage

```typescript
interface MilestoneComponent {
  name: string;              // e.g., "AnnotationParser"
  description: string;       // e.g., "Parses JSDoc annotations"
  milestoneId: string;      // e.g., "M1.2"
  sourceFile?: string;      // Optional: suggested implementation file
  required: boolean;        // Whether component is required for milestone completion
}

function extractComponents(milestoneContent: string): MilestoneComponent[] {
  const components: MilestoneComponent[] = [];

  // Strategy 1: Parse task breakdown section
  const taskSection = extractSection(milestoneContent, "Task Breakdown");
  const taskMatches = taskSection.match(/### Task \d+: ([A-Za-z_][A-Za-z0-9_]*)/g);

  // Strategy 2: Parse deliverables section
  const deliverables = extractSection(milestoneContent, "Deliverables");
  const deliverableMatches = deliverables.match(/\| ([A-Za-z_][A-Za-z0-9_]*) \|/g);

  // Strategy 3: Parse directory layout for file-based components
  const directoryLayout = extractSection(milestoneContent, "Directory Layout");
  const fileMatches = directoryLayout.match(/├── ([A-Za-z_][A-Za-z0-9_]*\.ts)/g);

  return components.filter(c => c.required); // Only count required components
}
```

### 🔍 Edge Case Handling

**Annotation Parsing Edge Cases**:

1. **Multi-line Annotations**:
```typescript
/**
 * @implements milestone-M1.2#AnnotationParser
 * @implements milestone-M1.2#ValidationEngine
 * Complex function with multiple implementations
 */
function complexFunction() { }
// Result: Creates 2 separate annotation objects
```

2. **Malformed Annotations**:
```typescript
/**
 * @implements milestone-M1.2  // Missing component name
 * @implements M1.2#Parser     // Missing "milestone-" prefix
 * @implements milestone-M1.2#123Invalid  // Invalid component name
 */
function badAnnotations() { }
// Result: Log warnings, skip invalid annotations, continue processing
```

3. **Nested Function Annotations**:
```typescript
class Parser {
  /**
   * @implements milestone-M1.2#AnnotationParser
   */
  parseAnnotations() {
    /**
     * @implements milestone-M1.2#HelperFunction  // Nested annotation
     */
    function helper() { }
  }
}
// Result: Both annotations are valid, create separate edges
```

**Graph Update Edge Cases**:

1. **File Rename/Move**:
```typescript
// Old: src/parser.ts -> New: src/annotation/parser.ts
// Strategy: Use git diff --find-renames to track file moves
// Update all annotation filePath references to new location
```

2. **Function Rename**:
```typescript
// Old: parseAnnotations() -> New: parseImplementsAnnotations()
// Strategy: Annotation stays with function, update functionName in graph
// Confidence remains 1.0 if annotation is still valid
```

3. **Merge Conflicts**:
```typescript
// If git diff fails due to merge conflicts:
// 1. Log warning about merge conflicts
// 2. Skip incremental update for conflicted files
// 3. Continue processing other files
// 4. Exit with code 1 (error) but don't fail entire build
```

**Coverage Calculation Edge Cases**:

1. **Optional Components**:
```typescript
// Some milestone components may be optional
// Only count required components in coverage calculation
const requiredComponents = components.filter(c => c.required);
coverage = implementedComponents.size / requiredComponents.length;
```

2. **Partial Implementation**:
```typescript
// Function exists but annotation has errors
// Count as 0.5 implementation for coverage
const partialImplementations = edges.filter(e => e.confidence === 0.5);
const fullImplementations = edges.filter(e => e.confidence >= 0.8);
const effectiveCoverage = (fullImplementations.length + partialImplementations.length * 0.5) / totalComponents;
```

3. **Stale Annotations**:
```typescript
// Don't count very stale annotations (confidence < 0.2) in coverage
const validImplementations = edges.filter(e => e.confidence >= 0.2);
coverage = validImplementations.length / totalComponents;
```

---

## 🔨 Task Breakdown

| #   | Branch                    | Task                                         | Owner | Est |
|-----|---------------------------|----------------------------------------------|-------|-----|
| 01  | M1.2/sync-lib-init        | Scaffold kg-sync-lib package (tsconfig, jest, interfaces). | BE | 1d |
| 02  | M1.2/git-diff-core        | Implement diffGit.ts with simple-git integration. | BE | 2d |
| 03  | M1.2/git-diff-edge-cases  | Add edge case handling (merge conflicts, renames, binary files). | BE | 1d |
| 04  | M1.2/annotation-parser    | Implement parseAnnotations.ts with comment-parser. | BE | 2d |
| 05  | M1.2/annotation-validation| Add validation rules, error handling, and format checking. | BE | 1d |
| 06  | M1.2/component-extraction | Implement extractComponents logic for milestone specs. | BE | 1d |
| 07  | M1.2/graph-update-core    | Implement updateGraph.ts with node/edge updates. | BE | 2d |
| 08  | M1.2/confidence-scoring   | Implement confidence calculation and stale detection. | BE | 2d |
| 09  | M1.2/coverage-calculation | Implement coverage metrics and threshold logic. | BE | 1d |
| 10  | M1.2/cli-integration      | Add sync-kg command to kg-cli with all parameters. | BE | 1d |
| 11  | M1.2/error-handling       | Implement comprehensive error handling and exit codes. | BE | 1d |
| 12  | M1.2/unit-tests          | Jest tests for all core functions (≥95% coverage). | BE | 2d |
| 13  | M1.2/integration-tests    | End-to-end tests for CLI and workflow scenarios. | BE | 1d |
| 14  | M1.2/performance-tests    | Performance benchmarking and optimization. | BE | 1d |
| 15  | M1.2/ci-workflow         | Add .github/workflows/bidirectional-sync.yml. | DevOps | 1d |
| 16  | M1.2/documentation       | Update README, add examples, write domain spec. | PM | 1d |
| 17  | M1.2/final-validation    | Run all acceptance tests, spec-lint, final review. | PM | 1d |
| 18  | M1.2/release             | Merge to main, tag kg-sync-v1.2.0, update docs. | Lead | 0.5d |

<Callout emoji="🗂">One PR per task. Reviewers tick acceptance hint in PR description.</Callout>

---

## 🤖 CLI Specification (sync-kg)

**Usage:**

```bash
pnpm run sync-kg [--since <commit-ish>] [--dry-run]
```

- Default `--since` = merge-base with origin/main.
- Outputs:
  - Updated kg.* (unless --dry-run)
  - kg-changes.json diff summary
  - Coverage table per milestone.
- Exit statuses:
  - 0 = success, coverage OK
  - 60 = coverage drop below threshold
  - 70 = annotation parse error
  - 1 = unexpected error

---

## 🤖 CI Workflow (.github/workflows/sync-diff.yml)

```yaml
name: KG Sync (PR)
on: pull_request

jobs:
  sync-diff:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with: { fetch-depth: 0 }     # need full history for diff
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && pnpm install
      - run: pnpm run sync-kg -- --since origin/main --dry-run
# Job fails if CLI exits non-zero (coverage breach or parse error).
```

---

## 🧪 Acceptance Tests

### 1️⃣ Annotation parse
```bash
node - <<'JS'
const { parseAnnotations } = require('code/packages/kg-sync-lib');
console.log(parseAnnotations('/** @implements milestone-M0#AuthService */').length === 1);
JS
```

### 2️⃣ Graph update
```bash
# Make a temp branch, add annotated fn, run sync-kg --since HEAD~1
pnpm run sync-kg -- --since HEAD~1
jq '.edges[] | select(.type=="implements")' kg.jsonld | wc -l  # >=1
```

### 3️⃣ Coverage threshold
```bash
# Remove an annotation, run sync-kg; CLI should set milestone coverage < 1 and still exit 0 (above 0.5). Remove a second, coverage <0.5, CLI exits 60.
```

### 4️⃣ CI green
```bash
# Push PR → sync-diff job passes.
```

---



## 🗂 Directory Layout

```
code/packages/kg-sync-lib/             # New package for bidirectional sync
├── src/
│   ├── diffGit.ts                     # Git diff detection using simple-git
│   ├── parseAnnotations.ts            # JSDoc annotation parsing
│   ├── updateGraph.ts                 # Incremental graph updates
│   └── index.ts                       # Package exports
├── tests/
│   ├── diffGit.test.ts                # Git diff tests
│   ├── parseAnnotations.test.ts       # Annotation parsing tests
│   └── updateGraph.test.ts            # Graph update tests
├── package.json                       # Package configuration
└── README.md                          # Package documentation

code/packages/kg-cli/                  # Enhanced CLI package
├── src/
│   ├── commands/
│   │   └── sync-kg.ts                 # New sync-kg command
│   └── bin/
│       └── kg-cli.ts                  # Updated CLI entry point
└── package.json                       # Updated with sync-kg command

.github/workflows/                     # CI/CD workflows
└── bidirectional-sync.yml             # New workflow for sync validation
```

---

## ✅ Success Criteria

### SC-1: Core Implementation (Tasks 1-11)
- [ ] **Package Setup** (Task 1): kg-sync-lib package scaffolded with TypeScript, Jest, and interfaces
- [ ] **Git Diff Core** (Tasks 2-3): diffGit.ts with simple-git integration and edge case handling
- [ ] **Annotation System** (Tasks 4-6): parseAnnotations.ts with validation and component extraction
- [ ] **Graph System** (Tasks 7-9): updateGraph.ts with confidence scoring and coverage calculation
- [ ] **CLI Integration** (Tasks 10-11): sync-kg command with comprehensive error handling

### SC-2: Testing & Quality Assurance (Tasks 12-14)
- [ ] **Unit Tests** (Task 12): ≥95% coverage for all core functions
- [ ] **Integration Tests** (Task 13): End-to-end CLI and workflow scenarios
- [ ] **Performance Tests** (Task 14): 90%+ improvement over full repository scans

### SC-3: Deployment & Documentation (Tasks 15-18)
- [ ] **CI Workflow** (Task 15): GitHub Actions workflow with coverage validation
- [ ] **Documentation** (Task 16): README, examples, and domain specification
- [ ] **Final Validation** (Task 17): All acceptance tests pass, spec-lint compliant
- [ ] **Release** (Task 18): Merged to main, tagged kg-sync-v1.2.0

### SC-4: Git Diff Detection
- [ ] Correctly identifies changed files since specified commit/branch
- [ ] Supports `--since HEAD~1`, `--since origin/main`, and working directory changes
- [ ] Handles edge cases: merge conflicts, binary files, large diffs
- [ ] Performance: 90%+ improvement over full repository scans

### SC-5: Annotation Parsing
- [ ] Correctly parses `@implements milestone-ID#Component` format
- [ ] 99%+ accuracy on test corpus with comprehensive error handling
- [ ] Supports TypeScript, JavaScript, and Python comment formats
- [ ] Validates milestone ID format: `M\d+(\.\d+)*` pattern

### SC-6: Incremental Graph Updates
- [ ] Merges new annotations into existing knowledge graph
- [ ] Implements confidence scoring and stale detection logic
- [ ] Maintains graph integrity during incremental updates
- [ ] Generates coverage metrics per milestone

### SC-7: CLI Integration
- [ ] `sync-kg` command available with `--since`, `--dry-run` parameters
- [ ] Exit codes: 0 (success), 60 (coverage breach), 70 (parse error), 1 (error)
- [ ] Comprehensive error reporting and performance metrics
- [ ] Integration with existing `kg-cli` package

### SC-8: Edge Case Handling
- [ ] File rename/move operations tracked correctly using git diff --find-renames
- [ ] Merge conflicts handled gracefully with appropriate error reporting
- [ ] Malformed annotations logged as warnings but don't break processing
- [ ] Multi-line and nested function annotations parsed correctly
- [ ] Component extraction works across task breakdown, deliverables, and directory layout

### SC-9: Technical Validation
- [ ] **Annotation Parsing**: 99%+ accuracy with comprehensive error handling
- [ ] **Confidence Scoring**: 5-level system with time-based degradation working correctly
- [ ] **Coverage Calculation**: Accurate milestone coverage with 50% threshold enforcement
- [ ] **Exit Codes**: Proper codes (0, 60, 70, 1) for all scenarios
- [ ] **Edge Cases**: File renames, merge conflicts, malformed annotations handled gracefully

**Completion Criteria**: When all success criteria are met, merge to main, tag `kg-sync-v1.2.0`, then open Milestone M2.

---

## 🤖 CI Pipeline

### Bidirectional Sync Workflow

```yaml
name: Bidirectional Sync Validation
on: [push, pull_request]

jobs:
  sync-validation:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for git diff

      - uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        run: cd code && pnpm install

      - name: Build packages
        run: cd code && pnpm build

      - name: Run incremental sync
        run: cd code && pnpm run sync-kg -- --since origin/main --dry-run ../docs/tech-specs

      - name: Validate coverage thresholds
        run: |
          # Check if coverage meets minimum thresholds
          # Exit 60 if coverage < 0.5, exit 0 otherwise
          cd code && pnpm run sync-kg -- --since origin/main ../docs/tech-specs

      - name: Run tests
        run: cd code && pnpm test

      - name: Check coverage
        run: cd code && pnpm run test:coverage
```

### Integration Points

- **Trigger**: All pushes and pull requests
- **Coverage Validation**: Fails if milestone coverage < 50%
- **Performance Monitoring**: Tracks sync operation performance
- **Artifact Generation**: Updated knowledge graph files

---

## 🔄 Document History

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 0.1.0 | 2025-05-29 | Initial milestone specification | WorkflowMapper Team |
| 0.2.0 | 2025-06-01 | Added missing sections for spec-lint compliance | WorkflowMapper Team |
| 0.3.0 | 2025-06-01 | Added detailed technical specifications for high-confidence execution | WorkflowMapper Team |
| 0.3.1 | 2025-06-01 | Updated task breakdown to reflect enhanced technical complexity | WorkflowMapper Team |
| 0.3.2 | 2025-06-01 | Updated success criteria to align with 18-task breakdown and enhanced implementation | WorkflowMapper Team |
| 0.3.3 | 2025-06-01 | Consolidated duplicate success criteria sections into single comprehensive section | WorkflowMapper Team |

